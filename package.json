{"name": "todo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "next lint", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@auth/drizzle-adapter": "^1.9.1", "@excalidraw/excalidraw": "^0.18.0", "@hookform/resolvers": "^5.0.1", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "browser-fs-access": "^0.35.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.42.0", "lucide-react": "^0.488.0", "nanoid": "^5.1.5", "next": "15.4.0-canary.37", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "react": "19.1.0", "react-day-picker": "8.10.1", "react-dom": "19.1.0", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.0", "zod": "^3.24.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^20.17.47", "@types/react": "19.1.1", "@types/react-dom": "19.1.2", "@types/react-syntax-highlighter": "^15.5.13", "drizzle-kit": "^0.31.1", "eslint": "^9.27.0", "eslint-config-next": "15.3.0", "tailwindcss": "^4.1.7", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "pnpm": {"overrides": {"@types/react": "19.1.1", "@types/react-dom": "19.1.2"}, "onlyBuiltDependencies": ["@tailwindcss/oxide", "esbuild", "unrs-resolver"]}, "peerDependencies": {"next": ">=14", "react": ">=18", "react-dom": ">=18"}}