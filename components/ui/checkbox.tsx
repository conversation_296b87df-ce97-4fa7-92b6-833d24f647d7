"use client"

import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check } from "lucide-react"

import { cn } from "@/lib/utils/utils"

const Checkbox = (
  {
    ref,
    className,
    ...props
  }: React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> & {
    ref?: React.RefObject<React.ComponentRef<typeof CheckboxPrimitive.Root>>;
  }
) => (<CheckboxPrimitive.Root
  ref={ref}
  className={cn(
    "peer h-4 w-4 shrink-0 rounded-sm border border-neutral-600 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-neutral-600 data-[state=checked]:text-primary-foreground",
    className,
  )}
  {...props}
>
  <CheckboxPrimitive.Indicator className={cn("flex items-center justify-center text-current")}>
    <Check className="h-3 w-3" />
  </CheckboxPrimitive.Indicator>
</CheckboxPrimitive.Root>)
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox }

