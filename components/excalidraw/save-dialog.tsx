import React from 'react'
import { <PERSON>ert<PERSON><PERSON>og, AlertDialogHeader, AlertDialogTrigger, AlertDialogContent, AlertDialogCancel } from '../ui/alert-dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { OrderedExcalidrawElement } from '@excalidraw/excalidraw/element/types'
import { saveDrawing, UpdateDrawing } from '@/lib/actions'
import { useFormStatus } from 'react-dom'
import { Label } from '../ui/label'

interface Props {
    elements: OrderedExcalidrawElement[] | null,
    drawingId?: string
}
export default function SaveDialog({ elements,drawingId }: Props) {

    return drawingId  ? (
      <form action={UpdateDrawing}>
        <input type='hidden' name='elements' value={JSON.stringify(elements)} />
        <input type='hidden' name='drawingId' value={drawingId} />
        <SubmitButton/>
      </form>
    ):(
        <AlertDialog>
            <AlertDialogTrigger asChild>
                <Button>Save</Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <form className='flex flex-col gap-4' action={saveDrawing}>
                        <input type='hidden' name='elements' value={JSON.stringify(elements)} />
                        <input type='hidden' name='drawingId' value={drawingId} />
                        <div className='space-y-2'>
                            <Label>Title<span className='text-red-500'>*</span></Label>
                            <Input placeholder='Enter your Title' name='title' />
                            <Label>Description</Label>
                            <Input placeholder='Enter your description' name='description' />
                        </div>
                        <div className='flex gap-2 items-center justify-center'>
                            <SubmitButton />
                            <AlertDialogCancel >
                                cancel

                            </AlertDialogCancel>
                        </div>
                    </form>
                </AlertDialogHeader>
            </AlertDialogContent>

        </AlertDialog>
    )
}

export function SubmitButton() {
    const { pending } = useFormStatus()
    return (
        <Button type="submit" disabled={pending} >{pending ? "Saving..." : "Save"}</Button>
    )
}